import { expect, Page } from "@playwright/test";
import { Widget } from "../Widget";
import { SubscriptionTierTitle } from "../../../types";

export class SubscriptionCards extends Widget {
  SUBSCRIPTION_TARIFF_CARD_ID = 'subscriptions-tariff-card';
  TARIFF_NAME_ID = 'tariff-name';
  SELECT_TARIFF_BUTTON_ID = 'select-tariff-button';

  constructor(page: Page) {
    super(page);
  }

  getTariffCards() {
    return this.page.getByTestId(this.SUBSCRIPTION_TARIFF_CARD_ID);
  }

  async getTariffCardByName(tariffName: SubscriptionTierTitle = SubscriptionTierTitle.ExtraSmall) {
    return this.getTariffCards()
      .filter({
        has: this.page.getByTestId(this.TARIFF_NAME_ID).getByText(tariffName, { exact: true }),
      })
      .describe(`tariff card with name ${tariffName}`);
  }

  async selectTariffByName(tariffName: SubscriptionTierTitle) {
    const card = await this.getTariffCardByName(tariffName);
    await expect(card).toBeVisible()
    const button = card.getByTestId(this.SELECT_TARIFF_BUTTON_ID);
    await expect(button).toBeVisible()
    await button.click();
  }
}
