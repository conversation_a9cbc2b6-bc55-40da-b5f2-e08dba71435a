import { Page } from '@playwright/test';
import { DashboardPage } from '../DashboardPage';
import { PrivateManagementModal } from '../widgets/subscription/privateManagementModal';

export class SubscriptionDetailPage extends DashboardPage {
  path = '/app/subscription/detail';

  currentPlanBlock = this.page.getByTestId('current-plan-block');
  manageSubscriptionButton = this.page.getByTestId('manage-subscription-btn');

  // upgrade
  // TODO move to side bar
  upgradeTariffButton = this.page.getByTestId('upgrade-tariff-btn');
  upgradeConnectButton = this.page.getByTestId('upgrade-subscription-btn');
  modalSuccessButton = this.page.getByTestId('modal-success-btn');

  get privateManagementModal() {
    return new PrivateManagementModal(this.page);
  }

  constructor(page: Page) {
    super(page);
  }

  async getCurrentTariffTitle() {
    const text =
      (await this.currentPlanBlock.getByTestId('private-block-content').textContent()) ?? '';
    return text.trim();
  }

  async cancelSubscriptionWithReason(reasonIndex = 0) {
    await this.manageSubscriptionButton.click();
    await this.privateManagementModal.dotsVerticalButton.click();
    await this.privateManagementModal.cancelSubscriptionButton.click();
    await this.privateManagementModal.cancelSubscriptionButtonLosePrivate.click();
    await this.privateManagementModal.cancelSubscriptionButtonAnyWay.click();
    const options = this.page.getByTestId('subscription-cancel-option').describe('Reason options');
    
    const tooExpensiveReason = options.nth(reasonIndex).locator('label');
    await tooExpensiveReason.waitFor({ state: 'visible' });
    await tooExpensiveReason.click();
    const submitButton = this.page.getByTestId('submit-feedback-button');
    await submitButton.click();
    const doneButton = this.page.getByTestId('subscription-cancel-done-button');
    await doneButton.click();
  }
}
