import { Page } from '@playwright/test';
import { DashboardPage } from '../DashboardPage';
import { SubscriptionBuyModal } from './SubscriptionBuyModal';
import { SubscriptionCards } from '../widgets/subscription/subscriptionCards';

export class SubscriptionPromoPage extends DashboardPage {
  path = '/app/subscription/promo';

  SUBSCRIPTION_TARIFF_CARD_ID = 'subscriptions-tariff-card';
  TARIFF_NAME_IO = 'tariff-name';
  SELECT_TARIFF_BUTTON_ID = 'select-tariff-button';

  get subscriptionBuyModal() {
    return new SubscriptionBuyModal(this.page);
  }

  get subscriptionCards() {
    return new SubscriptionCards(this.page);
  }

  constructor(page: Page) {
    super(page);
  }
}
