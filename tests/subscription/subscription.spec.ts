import { DashboardPage } from '../../support/pages/DashboardPage';
import { SubscriptionDetailPage } from '../../support/pages/subscribtion/SubscriptionDetailPage';
import { SubscriptionPromoPage } from '../../support/pages/subscribtion/SubscriptionPromoPage';
import { expect, test } from '../../support/fixtures/user.fixture';
import { SubscriptionTierTitle, VerificationTier } from '../../support/types';
import { waitForNetworkIdle } from '../../support/helpers/wait-for-network-idle';
import { performSubscription } from '../../support/reusable/commands/subscription/subscription';

test.describe('Subscription', { tag: ['@subscription'] }, () => {
  test.describe('With user with positive balance', () => {
    test('Should be able to add subscription', async ({ positiveUser, page }) => {

      await test.step('Prepare user', async () => {
        await positiveUser(VerificationTier.Scale);
      });


      await test.step('Open subscription page', async () => {
        await new SubscriptionPromoPage(page).goto();
      });

      const subscriptionPromoPage = new SubscriptionPromoPage(page);

      await test.step('Select tariff and buy', async () => {

        const card = await subscriptionPromoPage.subscriptionCards.getTariffCardByName(
          SubscriptionTierTitle.ExtraSmall
        );
        await expect(card).toBeVisible();
        const button = card.getByTestId('select-tariff-button');
        await expect(button).toBeVisible();
        await button.click();
        
      });

      await test.step('Connect account and confirm purchase', async () => {
        const { subscriptionBuyModal } = subscriptionPromoPage;
        await subscriptionBuyModal.connectAccountBtn.click();
        await subscriptionBuyModal.purchaseCompleteTakeLaterButton.click();
      });

      await test.step('Check subscription is added', async () => {
        const subscriptionDetailPage = new SubscriptionDetailPage(page);
        await subscriptionDetailPage.goto();

        const currentTariffTitle = await subscriptionDetailPage.getCurrentTariffTitle();
        expect(currentTariffTitle.toLowerCase()).toEqual(
          SubscriptionTierTitle.ExtraSmall.toLowerCase()
        );
      });
    });
  });

  test.describe('With user with subscription', () => {
    test.beforeEach(async ({ positiveUser }) => {
      const { page } = await positiveUser(VerificationTier.Scale);
      await new SubscriptionPromoPage(page).goto();
      await performSubscription(page, SubscriptionTierTitle.ExtraSmall);
    });

    test('Should be able to cancel subscription', async ({ page }) => {
      const subscriptionDetailPage = new SubscriptionDetailPage(page);
      await subscriptionDetailPage.goto();
      await subscriptionDetailPage.cancelSubscriptionWithReason(0);

      const dashboardPage = new DashboardPage(page);
      await dashboardPage.goto();
      await waitForNetworkIdle(page);
      await expect(dashboardPage.subscriptionBlock).toBeHidden();
    });

    test('Should be able to switch subscription plan', async ({ page }) => {
      const subscriptionDetailPage = new SubscriptionDetailPage(page);
      await subscriptionDetailPage.goto();
      await subscriptionDetailPage.manageSubscriptionButton.click();
      await subscriptionDetailPage.upgradeTariffButton.click();
      await subscriptionDetailPage.upgradeTariffByName(SubscriptionTierTitle.Small);
      await subscriptionDetailPage.upgradeConnectButton.click();
      await subscriptionDetailPage.modalSuccessButton.click();

      const currentTariffTitle = await subscriptionDetailPage.getCurrentTariffTitle();
      expect(currentTariffTitle.toLowerCase()).toEqual(SubscriptionTierTitle.Small.toLowerCase());
    });
  });
});
