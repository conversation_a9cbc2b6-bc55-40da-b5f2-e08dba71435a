import { Page } from "@playwright/test";
import { Widget } from "../Widget";

export class PrivateManagementModal extends Widget {

  upgradeButton = this.page.getByTestId('upgrade-tariff-btn');

  // cancel
  dotsVerticalButton = this.page.getByTestId('dots-vertical-icon');
  cancelSubscriptionButton = this.page.getByTestId('cancel-subscription-btn');
  cancelSubscriptionButtonLosePrivate = this.page.getByTestId(
    'cancel-subscription-button-lose-private'
  );
  cancelSubscriptionButtonAnyWay = this.page.getByTestId('cancel-subscription-anyway-button');

  constructor(page: Page) {
    super(page);
  }

  // cancel reason step
  async selectCancelReason(reasonIndex = 0) {
    const options = this.page.getByTestId('subscription-cancel-option').describe('Reason options');
    const tooExpensiveReason = options.nth(reasonIndex).locator('label');
    await tooExpensiveReason.waitFor({ state: 'visible' });
    await tooExpensiveReason.click();
    const submitButton = this.page.getByTestId('submit-feedback-button');
    await submitButton.click();
    const doneButton = this.page.getByTestId('subscription-cancel-done-button');
    await doneButton.click();
  }
}
