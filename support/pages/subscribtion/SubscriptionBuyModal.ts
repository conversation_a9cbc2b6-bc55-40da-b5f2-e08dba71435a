import { Page } from '@playwright/test';
import { Widget } from '../widgets/Widget';

export class SubscriptionBuyModal extends Widget {

  ACCOUNTS_AND_CARDS_SELECT_CONTAINER_ID = 'accounts-and-cards-select';
  PAYMENT_METHOD_SELECT_ID = 'ui-select';
  ACCOUNT_SELECT_OPTION_ID = 'accounts-and-cards-select-option-account';

  topUpAccountBalanceBtn = this.page.getByTestId('top-up-account-btn');
  totalAmountRow = this.page.getByTestId('total-amount-row > p').nth(1);
  connectAccountBtn = this.page.getByTestId('connect-account-btn');
  purchaseCompleteButton = this.page.getByTestId('purchase-complete-button');
  purchaseCompleteTakeLaterButton = this.page
    .getByTestId('purchase-complete-take-later-button')
    .describe('Success ok button');

  constructor(page: Page) {
    super(page);
  }

  async getTotalAmount() {
    const text = await this.totalAmountRow.textContent();
    const number = text?.replace(/[^0-9.]/g, '');
    return parseFloat(number ?? '');
  }
}
