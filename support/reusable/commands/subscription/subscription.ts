import { expect, Page } from '@playwright/test';
import { DashboardPage } from '../../../pages/DashboardPage';
import { SubscriptionPromoPage } from '../../../pages/subscribtion/SubscriptionPromoPage';
import { SubscriptionTierTitle } from '../../../types';

export const performSubscription = async (
  page: Page,
  tariffName: SubscriptionTierTitle = SubscriptionTierTitle.ExtraSmall
) => {
  const subscriptionPromoPage = new SubscriptionPromoPage(page);
  await subscriptionPromoPage.goto();
  await subscriptionPromoPage.subscriptionCards.selectTariffByName(tariffName);

  const { subscriptionBuyModal } = subscriptionPromoPage;
  await subscriptionBuyModal.connectAccountBtn.click();
  await subscriptionBuyModal.purchaseCompleteTakeLaterButton.click();
  await new DashboardPage(page).goto();
  await expect(page).toHaveURL(/.*dashboard.*/);
};
